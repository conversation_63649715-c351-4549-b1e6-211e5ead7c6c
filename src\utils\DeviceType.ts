import { sendToEnvironment } from './microAppUtils';

export enum DeviceType {
  Mobile,
  Desktop,
  TV,
}

// 定义 appInfo 的类型
interface AppInfo {
  deviceType?: string;
  channel?: string;
  [key: string]: any; // 允许扩展其他字段
}

export interface WebDavInfo {
  username: string,//账号
  password: string, // 密码
  uri: string, // 绝对路径可截取部分
  alias_root: string,
  port: number,
  port_2?: number
}

interface DeviceInfo {
  cgiPort: string | number, // 端侧 CGI 端口
  deviceIp: string,
  webdavPort: string | number,
  deviceToken?: string,
  [key: string]: any; // 允许扩展其他字段
}

export interface AccountInfo {
  uid: string; // 账号
  icon: string;
  nickName: string;
}

let appInfo: AppInfo | null = null;
let webDavInfo: WebDavInfo | null = null;
let deviceInfo: DeviceInfo | null = null;
let accountInfo: AccountInfo | null = null;
/**
 * 获取设备类型（优先从 URL 参数和缓存中获取）
 * @returns DeviceType - 设备类型
 */
export const getDeviceType = (): DeviceType => {
  // 尝试从 localStorage 中读取设备类型
  const cachedDeviceType = localStorage.getItem('deviceType');
  if (cachedDeviceType) {
    return parseInt(cachedDeviceType, 10) as DeviceType;
  }

  // 从 URL 参数中获取设备类型
  const urlParams = window.location.href;
  const urlDeviceType = urlParams.includes('_app') ? 'mobile' : urlParams.includes('_pc') ? 'desktop' : urlParams.includes('_tv') ? 'tv' : null;

  if (urlDeviceType) {
    switch (urlDeviceType.toLowerCase()) {
      case 'mobile':
        return DeviceType.Mobile;
      case 'desktop':
        return DeviceType.Desktop;
      case 'tv':
        return DeviceType.TV;
      case 'ipad':
        return DeviceType.Mobile;
      default:
        console.warn(`未知的设备类型: ${urlDeviceType}`);
    }
  }


  // 回退到全局变量 appInfo
  if (appInfo && appInfo.deviceType) {
    switch (appInfo.deviceType.toLowerCase()) {
      case 'mobile':
        return DeviceType.Mobile;
      case 'iPad':
        return DeviceType.Mobile;
      case 'pc':
        return DeviceType.Desktop;
      case 'tv':
        return DeviceType.TV;
      default:
        console.warn(`未知的设备类型: ${appInfo.deviceType}`);
    }
  }

  // 如果以上都无法获取，返回默认值
  return DeviceType.Mobile;
};
// 获取系统类型
export const getSystemType = (): string => {
  // 尝试从 appInfo 中获取系统类型
  if (appInfo && appInfo.channel) {
    return appInfo.channel;
  }

  // 使用 navigator.userAgent 检测系统类型
  const userAgent = navigator.userAgent.toLowerCase();

  if (/android/.test(userAgent)) {
    return "android";
  }
  if (/iphone|ipad|ipod/.test(userAgent)) {
    return "ios";
  }
  if (/macintosh|mac os x/.test(userAgent)) {
    return "macos";
  }
  if (/windows/.test(userAgent)) {
    return "windows";
  }
  return 'unknown';
};

export const getWebDavInfo = (): WebDavInfo => {
  if (webDavInfo) return webDavInfo;

  return {
    "username": "u2545476901",
    "password": "WL-pR5wlaB@R6wlNCrSH)YNC#TIxZcDsT8_nO3tU8+~d3%j9+peF^W0A!eG&kLB@", // 密码
    "uri": "/",
    "alias_root": "/home/<USER>",
    "port": 5000,
    "port_2": 8081
  };
}

export const fetchWebDavInfo = async (): Promise<void> => {
  return new Promise((rs, rj) => {
    sendToEnvironment(
      { methodName: "IPC_getWebDavInfo", params: {} },
      { params: { cmd: "getWebDavInfo" } },
      (response) => {
        console.log('获取webDav info的响应:', JSON.stringify(response));
        // 处理响应
        if (response.code === 0 || response.cmd === 'getWebDavInfo') {
          if (response.cmd) {
            webDavInfo = response.webDav
          } else {
            webDavInfo = response.data.webDav
          }
          localStorage.setItem('webDavInfo', JSON.stringify(webDavInfo));
          rs();
        } else {
          rj(new Error(`获取webDav info失败: ${response.message || '未知错误'}`));
        }
      }
    );
  })
}

/**
 * 异步获取端侧基本信息
 * @returns Promise<void>
 */
export const fetchAppInfo = async (): Promise<void> => {
  if (appInfo) {
    console.log('使用缓存的 appInfo:', appInfo);
    return Promise.resolve();
  }

  // 尝试从 localStorage 中读取缓存
  const cachedAppInfo = localStorage.getItem('appInfo');
  if (cachedAppInfo) {
    appInfo = JSON.parse(cachedAppInfo);
    console.log('从缓存中加载的 appInfo:', appInfo);
    return Promise.resolve();
  }

  // 如果缓存不存在，则通过网络请求获取
  return new Promise((resolve, reject) => {
    sendToEnvironment(
      { methodName: "normal_getAppInfo", params: {} }, // app端调用
      { params: { cmd: "getPcInfo" } }, // pc端调用
      (response) => {
        console.log('获取app info:', JSON.stringify(response));
        // 处理响应
        if (response.code === 0 || response.cmd === 'getPcInfo') {
          appInfo = response.data;
          localStorage.setItem('appInfo', JSON.stringify(appInfo));
          // 根据设备类型映射 deviceType
          const deviceTypeMap: Record<string, string> = {
            mobile: '0',
            iPad: '0',
            pc: '1',
            tv: '2',
          };

          const deviceType = appInfo?.deviceType ? deviceTypeMap[appInfo.deviceType] || '' : '';
          // 如果 deviceType 不为空，则存储到 localStorage
          if (deviceType) {
            localStorage.setItem('deviceType', deviceType);
          } else {
            console.warn(`app info中deviceType不存在,未知设备类型: ${appInfo?.deviceType}`);
          }
          resolve();
        } else {
          reject(new Error(`获取app info 失败: ${response.message || '未知错误'}`));
        }

      }
    );
  });
};
//获取设备信息
export const getDeviceInfo = (): DeviceInfo => {
  if (deviceInfo) return deviceInfo;
  return {
    cgiPort: "443", // 默认端侧 CGI 端口
    deviceIp: "127.0.0.1",
    webdavPort: "5000",
    deviceToken: "xxxxx",
    cgiToken: "L8tkb4Ug8uQc4Vg9ulI4VM9vRIzVN0vR"
  }
}

export const fetchDeviceInfo = async (type: string): Promise<void> => {
  return new Promise((resolve, reject) => {
    sendToEnvironment(
      { methodName: "normal_getDeviceInfo", params: { type: type } },
      { params: { cmd: "getDeviceInfo", type: type } },
      (response) => {
        console.log('获取device info的响应:', JSON.stringify(response));
        // 处理响应
        if (response.code === 0 || response.cmd === 'getDeviceInfo') {
          if (response.cmd) {
            deviceInfo = response.deviceInfo;
          } else {
            deviceInfo = response.data;
          }
          localStorage.setItem('deviceInfo', JSON.stringify(deviceInfo));
          resolve();
        } else {
          reject(new Error(`获取device info失败: ${response.message || '未知错误'}`));
        }
      }
    );
  });
}
//获取账号信息
export const getAccountInfo = (): AccountInfo => {
  if (accountInfo) return accountInfo;
  const url = window.location.href;
  //获取plugin 后面的值为 uid
  const uidMatch = url.match(/plugin\/([^/]+)/);
  const accountObj: AccountInfo = {
    uid: 'default_user',
    nickName: 'default_user',
    icon: ''
  };
  if (uidMatch && uidMatch[1]) {
    return { ...accountObj, uid: uidMatch[1] }; // 返回从 URL 中提取的 uid
  }
  return accountObj
}
// 存储用户列表信息
let userListInfo: any[] = [];

export const fetchAccountInfo = async (): Promise<void> => {
  return new Promise((resolve, reject) => {
    sendToEnvironment(
      { methodName: "normal_getAccountInfo", params: {} },
      { params: { cmd: "getUserInfo" } },
      (response) => {
        console.log('获取user info的响应:', JSON.stringify(response));
        // 处理响应
        if (response.code === 0 || response.cmd === 'getUserInfo') {
          if (response.cmd) {
            accountInfo = response.userInfo;
            // 额外存储用户列表信息
            userListInfo = response.userInfo || [];
          } else {
            accountInfo = response.data;
            // 额外存储用户列表信息
            userListInfo = response.data?.userInfo || [];
          }
          localStorage.setItem('accountInfo', JSON.stringify(accountInfo));
          // 存储用户列表信息到localStorage
          localStorage.setItem('userListInfo', JSON.stringify(userListInfo));
          resolve();
        } else {
          reject(new Error(`获取user info失败: ${response.message || '未知错误'}`));
        }
      }
    );
  });
}

// 导出获取用户列表的函数
export const getUserListInfo = (): any[] => {
  try {
    const stored = localStorage.getItem('userListInfo');
    return stored ? JSON.parse(stored) : userListInfo;
  } catch (error) {
    console.error('获取用户列表信息失败:', error);
    return userListInfo;
  }
}