import React, { useState, useCallback, useMemo } from 'react';
import FileList, { FileItem } from './index';

// 生成测试数据的工具函数
const generateTestData = (count: number, startId: number = 0): FileItem[] => {
  const fileTypes = ['file', 'folder'] as const;
  const categories = [1, 2, 3, 4, 5, 6, 7];
  const extensions = ['.txt', '.pdf', '.jpg', '.mp4', '.zip', '.doc', '.xls'];
  
  return Array.from({ length: count }, (_, index) => {
    const id = startId + index;
    const type = fileTypes[Math.floor(Math.random() * fileTypes.length)];
    const category = categories[Math.floor(Math.random() * categories.length)];
    const extension = extensions[Math.floor(Math.random() * extensions.length)];
    
    return {
      id,
      name: type === 'folder' ? `文件夹${id}` : `文件${id}${extension}`,
      type,
      time: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toLocaleDateString(),
      icon: '',
      category,
      size: type === 'file' ? Math.floor(Math.random() * 1000000) : undefined,
    };
  });
};

// 性能优化示例组件
const PerformanceExample: React.FC = () => {
  const [fileData, setFileData] = useState<FileItem[]>(() => generateTestData(50));
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [selectedIds, setSelectedIds] = useState<number[]>([]);

  // 模拟加载更多数据
  const loadMoreData = useCallback(async () => {
    if (loading) return;
    
    setLoading(true);
    
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const newData = generateTestData(20, fileData.length);
    setFileData(prev => [...prev, ...newData]);
    
    // 模拟数据加载完毕
    if (fileData.length >= 200) {
      setHasMore(false);
    }
    
    setLoading(false);
  }, [fileData.length, loading]);

  // 处理文件点击
  const handleItemClick = useCallback((item: FileItem) => {
    console.log('点击文件:', item.name);
  }, []);

  // 处理下载
  const handleDownload = useCallback((ids: number[]) => {
    console.log('下载文件 IDs:', ids);
  }, []);

  // 处理选择变化
  const handleSelectionChange = useCallback((ids: number[]) => {
    setSelectedIds(ids);
  }, []);

  // 重置数据
  const resetData = useCallback(() => {
    setFileData(generateTestData(50));
    setHasMore(true);
    setSelectedIds([]);
  }, []);

  // 添加大量数据测试
  const addLargeDataSet = useCallback(() => {
    const largeData = generateTestData(500);
    setFileData(largeData);
    setHasMore(false);
  }, []);

  const stats = useMemo(() => ({
    totalFiles: fileData.length,
    selectedCount: selectedIds.length,
    folders: fileData.filter(f => f.type === 'folder').length,
    files: fileData.filter(f => f.type === 'file').length,
  }), [fileData, selectedIds]);

  return (
    <div style={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
      {/* 控制面板 */}
      <div style={{ 
        padding: '16px', 
        borderBottom: '1px solid #eee',
        backgroundColor: '#f5f5f5'
      }}>
        <h3>FileList 性能测试</h3>
        <div style={{ marginBottom: '12px' }}>
          <strong>统计信息：</strong>
          <span style={{ marginLeft: '8px' }}>
            总计: {stats.totalFiles} | 
            文件夹: {stats.folders} | 
            文件: {stats.files} | 
            已选择: {stats.selectedCount}
          </span>
        </div>
        <div style={{ display: 'flex', gap: '8px', flexWrap: 'wrap' }}>
          <button onClick={resetData}>重置数据 (50条)</button>
          <button onClick={addLargeDataSet}>大数据测试 (500条)</button>
          <button onClick={loadMoreData} disabled={loading || !hasMore}>
            {loading ? '加载中...' : '手动加载更多'}
          </button>
        </div>
      </div>

      {/* 文件列表 */}
      <div style={{ flex: 1, overflow: 'hidden' }}>
        <FileList
          data={fileData}
          hasMore={hasMore}
          onLoadMore={loadMoreData}
          onItemClick={handleItemClick}
          onDownload={handleDownload}
          onSelectionChange={handleSelectionChange}
          externalSelectedIds={selectedIds}
          editable={true}
          loading={loading}
          currentPath="/test"
        />
      </div>
    </div>
  );
};

export default PerformanceExample;

// 性能测试工具
export const performanceTest = {
  // 测试渲染时间
  measureRenderTime: (componentName: string, renderFn: () => void) => {
    const start = performance.now();
    renderFn();
    const end = performance.now();
    console.log(`${componentName} 渲染时间: ${end - start}ms`);
  },

  // 测试内存使用
  measureMemoryUsage: () => {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      return {
        used: Math.round(memory.usedJSHeapSize / 1048576 * 100) / 100,
        total: Math.round(memory.totalJSHeapSize / 1048576 * 100) / 100,
        limit: Math.round(memory.jsHeapSizeLimit / 1048576 * 100) / 100,
      };
    }
    return null;
  },

  // 测试滚动性能
  measureScrollPerformance: (element: HTMLElement, duration: number = 5000) => {
    let frameCount = 0;
    const startTime = performance.now();
    
    const countFrames = () => {
      frameCount++;
      if (performance.now() - startTime < duration) {
        requestAnimationFrame(countFrames);
      } else {
        const fps = frameCount / (duration / 1000);
        console.log(`平均 FPS: ${fps.toFixed(2)}`);
      }
    };
    
    requestAnimationFrame(countFrames);
  }
};
