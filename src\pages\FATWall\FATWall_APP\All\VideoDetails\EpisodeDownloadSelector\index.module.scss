// 全屏覆盖遮罩
.overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--background-color);
    z-index: 2;
    display: flex;
    flex-direction: column;
}

// 主容器
.container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    background-color: #000;
}

// 头部区域
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 44px 0px 20px 20px; // 增加顶部padding，避免状态栏遮挡
    border-bottom: 1px solid var(--border-color);
    flex-shrink: 0;
}

.title {
    font-size: 30px;
    padding-left: 20px;
    font-weight: 600;
    color: #fff;
    margin: 0;
    font-family: 'MiSans', sans-serif;
}

.closeButton {
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    
    &:hover {
        background-color: var(--hover-background-color);
    }
    
    &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }
}

// 集数网格区域
.episodeGrid {
    display: grid;
    gap: 12px;
    flex-wrap: wrap;
    padding: 10px;
    grid-template-columns: repeat(5, 1fr);
    overflow-y: auto;
    // 添加滚动条样式
    &::-webkit-scrollbar {
        width: 4px;
    }
    
    &::-webkit-scrollbar-track {
        background: transparent;
    }
    
    &::-webkit-scrollbar-thumb {
        background-color: rgba(255, 255, 255, 0.3);
        border-radius: 2px;
    }
}

// 集数项目
.episodeItem {
    aspect-ratio: 1; // 保持正方形
    width: 60px;
    height: 60px;
    background-color: #303030; // 按照版本选择器样式规范：未选中项背景色为#303030
    border: 1px solid transparent;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    
    &:hover {
        background-color: rgba(0, 132, 255, 0.1); // hover效果使用rgba实现透明度的轻微变化
        border-color: #0084FF;
    }
    
    &:active {
        transform: scale(0.95);
        background-color: #0084FF; // 按照版本选择器样式规范：选中项背景色为#0084FF
        
        .episodeNumber {
            color: #ffffff;
        }
    }
}

.episodeNumber {
    font-size: 16px;
    font-weight: 500;
    color: #ffffff;
    user-select: none;
    font-family: 'MiSans', sans-serif;
}

// 底部区域
.footer {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 16px 20px 20px 20px;
    border-top: 1px solid var(--border-color);
    flex-shrink: 0;
}

.downloadAllButton {
    width: 100%;
    height: 44px;
    background-color: #000;
    color: #0D84FF;
    display: flex;
    justify-content: center;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s ease;
    font-family: 'MiSans', sans-serif;
}

// Modal按钮样式（用于下载位置弹窗）
.modalButton {
    width: 100%;
    height: 44px;
    background-color: #0084FF;
    color: #ffffff;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 20px;
    font-family: 'MiSans', sans-serif;
    
    &:hover {
        background-color: #0066CC;
    }
    
    &:active {
        background-color: #0052A3;
    }
}
.cardList { 
    display: flex;
    flex-wrap: wrap;
        overflow-y: auto;
        margin-bottom: 80px;
}
// 电影卡片样式
.cardItem {
    display: flex;
    align-items: center;
    width: 100%;
    // background-color: rgba(48, 48, 48, 0.8);
    border-radius: 12px;
    padding: 12px;
    margin-bottom: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    
    &:hover {
        background-color: rgba(48, 48, 48, 1);
    }
    
    &:active {
        transform: scale(0.98);
        background-color: rgba(68, 68, 68, 1);
    }
}

.imgsize {
    width: 145px;
    height: 80px;
    border-radius: 8px;
    object-fit: cover;
    margin-right: 12px;
    flex-shrink: 0;
}

.cardItem > div {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    
}

.cardItem .episodeNumber {
    font-size: 16px;
    font-weight: 500;
    color: #ffffff;
    margin-bottom: 4px;
    font-family: 'MiSans', sans-serif;
}

.cardItem span:last-child {
    font-size: 13px;
    color: rgba(255, 255, 255, 0.7);
    font-family: 'MiSans', sans-serif;
}
