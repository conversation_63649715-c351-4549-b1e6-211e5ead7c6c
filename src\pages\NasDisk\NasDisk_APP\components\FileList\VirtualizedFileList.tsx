import React, { useState, useCallback, useMemo, useRef, useEffect } from 'react';
import { Checkbox, Button, Toast } from 'antd-mobile';
import { FileItem, FileListProps } from './index';
import styles from './index.module.scss';
import { PreloadImage } from '@/components/Image';
import { getFileType, FileTypes } from '@/utils/fileTypeUtils';

// 导入图标
import fileIcon from '@/Resources/nasDiskImg/file-icon.png';
import folderIcon from '@/Resources/nasDiskImg/file-icon.png';
import audioIcon from '@/Resources/nasDiskImg/musicIcon.png';
import docIcon from '@/Resources/nasDiskImg/textIcon.png';
import appIcon from '@/Resources/nasDiskImg/otherIcon.png';
import btIcon from '@/Resources/nasDiskImg/btIcon.png';
import pdfIcon from '@/Resources/nasDiskImg/pdfIcon.png';
import zipIcon from '@/Resources/nasDiskImg/zipIcon.png';
import pptIcon from '@/Resources/nasDiskImg/pptIcon.png';
import wordIcon from '@/Resources/nasDiskImg/wordIcon.png';
import xlsIcon from '@/Resources/nasDiskImg/xlsIcon.png';
import textIcon from '@/Resources/nasDiskImg/textIcon.png';

// 虚拟滚动配置
const ITEM_HEIGHT = 72; // 每个文件项的高度
const BUFFER_SIZE = 5; // 缓冲区大小（上下各渲染几个额外项目）

interface VirtualizedFileListProps extends Omit<FileListProps, 'data'> {
  data: FileItem[];
  containerHeight?: number; // 容器高度，默认为视窗高度
}

// 虚拟化文件列表组件
const VirtualizedFileList: React.FC<VirtualizedFileListProps> = ({
  data,
  containerHeight,
  editable = true,
  onItemClick,
  externalSelectedIds = [],
  onSelectionChange,
  ...otherProps
}) => {
  const [scrollTop, setScrollTop] = useState(0);
  const [selectedIds, setSelectedIds] = useState<number[]>(externalSelectedIds);
  const containerRef = useRef<HTMLDivElement>(null);
  const [containerSize, setContainerSize] = useState({ width: 0, height: containerHeight || 600 });

  // 同步外部选中状态
  useEffect(() => {
    setSelectedIds(externalSelectedIds);
  }, [externalSelectedIds]);

  // 获取容器尺寸
  useEffect(() => {
    const updateSize = () => {
      if (containerRef.current) {
        const rect = containerRef.current.getBoundingClientRect();
        setContainerSize({
          width: rect.width,
          height: containerHeight || rect.height || 600
        });
      }
    };

    updateSize();
    window.addEventListener('resize', updateSize);
    return () => window.removeEventListener('resize', updateSize);
  }, [containerHeight]);

  // 图标缓存
  const getFileIcon = useMemo(() => {
    const iconCache = new Map<string, string>();
    
    return (file: FileItem) => {
      const cacheKey = `${file.id}-${file.type}-${file.category}-${file.name}`;
      
      if (iconCache.has(cacheKey)) {
        return iconCache.get(cacheKey)!;
      }
      
      let icon: string;
      
      if (file.type === "folder") {
        icon = folderIcon;
      } else if ((file.category === 3 || file.category === 1) && file.thumbs) {
        icon = file.thumbs.icon || file.thumbs.url1 || file.thumbs.url2 || file.thumbs.url3 || fileIcon;
      } else {
        const fileType = getFileType(file.name, false);
        
        switch (fileType) {
          case FileTypes.PDF: icon = pdfIcon; break;
          case FileTypes.ZIP: icon = zipIcon; break;
          case FileTypes.WORD: icon = wordIcon; break;
          case FileTypes.EXCEL: icon = xlsIcon; break;
          case FileTypes.PPT: icon = pptIcon; break;
          case FileTypes.TEXT: icon = textIcon; break;
          case FileTypes.AUDIO: icon = audioIcon; break;
          default:
            switch (file.category) {
              case 2: icon = audioIcon; break;
              case 4: icon = docIcon; break;
              case 5: icon = appIcon; break;
              case 7: icon = btIcon; break;
              default: icon = fileIcon;
            }
        }
      }
      
      iconCache.set(cacheKey, icon);
      return icon;
    };
  }, []);

  // 计算可见项目范围
  const visibleRange = useMemo(() => {
    const visibleCount = Math.ceil(containerSize.height / ITEM_HEIGHT);
    const startIndex = Math.max(0, Math.floor(scrollTop / ITEM_HEIGHT) - BUFFER_SIZE);
    const endIndex = Math.min(data.length - 1, startIndex + visibleCount + BUFFER_SIZE * 2);
    
    return { startIndex, endIndex, visibleCount };
  }, [scrollTop, containerSize.height, data.length]);

  // 处理滚动
  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    setScrollTop(e.currentTarget.scrollTop);
  }, []);

  // 处理文件点击
  const handleItemClick = useCallback((file: FileItem) => {
    if (onItemClick) {
      onItemClick(file);
    }
  }, [onItemClick]);

  // 处理选择变化
  const handleCheckboxChange = useCallback((id: number, checked: boolean) => {
    const newSelectedIds = checked 
      ? [...selectedIds, id]
      : selectedIds.filter(item => item !== id);
    
    setSelectedIds(newSelectedIds);
    if (onSelectionChange) {
      onSelectionChange(newSelectedIds);
    }
  }, [selectedIds, onSelectionChange]);

  // 渲染可见项目
  const visibleItems = useMemo(() => {
    const items = [];
    const { startIndex, endIndex } = visibleRange;
    
    for (let i = startIndex; i <= endIndex; i++) {
      const file = data[i];
      if (!file) continue;
      
      const isSelected = selectedIds.includes(file.id);
      const top = i * ITEM_HEIGHT;
      
      items.push(
        <div
          key={file.id}
          className={styles.virtualItem}
          style={{
            position: 'absolute',
            top,
            left: 0,
            right: 0,
            height: ITEM_HEIGHT,
            display: 'flex',
            alignItems: 'center',
            padding: '12px 16px',
            borderBottom: '1px solid #f0f0f0',
            backgroundColor: 'var(--background-color)',
          }}
          onClick={() => handleItemClick(file)}
        >
          <div className={styles.fileIcon}>
            <PreloadImage
              src={getFileIcon(file)}
              alt=""
              style={{ width: 40, height: 40, borderRadius: 5 }}
            />
          </div>
          
          <div className={styles.fileInfo} style={{ flex: 1, marginLeft: 12 }}>
            <div className={styles.fileName}>{file.name}</div>
            <div className={styles.fileTime}>{file.time}</div>
          </div>
          
          {editable && (
            <Checkbox
              checked={isSelected}
              onChange={(checked) => handleCheckboxChange(file.id, checked)}
              onClick={(e) => e.stopPropagation()}
            />
          )}
        </div>
      );
    }
    
    return items;
  }, [visibleRange, data, selectedIds, editable, getFileIcon, handleItemClick, handleCheckboxChange]);

  const totalHeight = data.length * ITEM_HEIGHT;

  return (
    <div className={styles.ListContainer}>
      <div
        ref={containerRef}
        style={{
          height: containerSize.height,
          overflow: 'auto',
          position: 'relative',
        }}
        onScroll={handleScroll}
      >
        {/* 虚拟滚动容器 */}
        <div style={{ height: totalHeight, position: 'relative' }}>
          {visibleItems}
        </div>
      </div>
      
      {/* 底部操作按钮 */}
      {editable && selectedIds.length > 0 && (
        <div className={styles.footerButtons}>
          <Button
            block
            color="primary"
            className={styles.downloadButton}
            onClick={() => {
              Toast.show(`选中了 ${selectedIds.length} 个文件`);
            }}
          >
            下载 ({selectedIds.length})
          </Button>
        </div>
      )}
    </div>
  );
};

export default VirtualizedFileList;
